# Historia zmian wtyczki Papierotka Custom Order

## [1.3.1] - 2025-04-16
### Ulepszono
- Dynamiczne pobieranie tytułów stron z adresu URL
  - Zmodyfikowano skrypty JavaScript, aby automatycznie wykrywały tytuł strony na podstawie URL
  - Zmieniono nazwę strony formularza z "Formularz zamówienia" na "Zamówienie" zgodnie z URL
  - Dodano inteligentne parsowanie ostatniej części adresu URL
  - Ulepszono mechanizm wykrywania kontekstu strony
  - Dodano obsługę dla dowolnych stron niestandardowych w przyszłości

## [1.3.0] - 2025-04-16
### Poprawiono
- Naprawiono wyświetlanie tytułów i breadcrumbs na stronie formularza zamówienia
  - Zmodyfikowano skrypty `breadcrumbs-fix.js` i `breadcrumbs-direct-fix.js`, aby poprawnie obsługiwały zarówno stronę formularza zamówienia (/zamowienie) jak i stronę potwierdzenia zamówienia (/potwierdzenie-zamowienia)
  - Dodano inteligentne wykrywanie, na której stronie znajduje się użytkownik
  - Dodano automatyczną korektę nagłówków H1 na stronie formularza zamówienia
  - Ulepszono logikę wykrywania i modyfikacji elementów breadcrumbs
  - Dodano dodatkowe komunikaty debugowania w konsoli przeglądarki

## [1.2.9] - 2025-04-16
### Poprawiono
- Kompleksowe rozwiązanie problemu z breadcrumbs na stronie potwierdzenia zamówienia
- Dodano dedykowany skrypt JavaScript do naprawy wyświetlania breadcrumbs
- Zapewniono kompatybilność z przyszłą stroną bloga

### Techniczne
- Utworzono nowy plik `breadcrumbs-fix.js` z dedykowaną logiką naprawy breadcrumbs
- Zaimplementowano inteligentne wykrywanie kontekstu strony przez URL
- Zastosowano precyzyjne celowanie w elementy breadcrumbs
- Dodano zabezpieczenia przed modyfikacją rzeczywistej strony bloga

## [1.2.8] - 2025-04-16
### Poprawiono
- Rozszerzono rozwiązanie problemu z tytułem strony potwierdzenia zamówienia
- Dodano bezpośrednią manipulację DOM za pomocą JavaScript do zmiany tytułu "Blog"
- Zastosowano wielopoziomowe podejście do zmiany tytułu strony

### Techniczne
- Dodano filtr `the_title` bezpośrednio w szablonie strony potwierdzenia zamówienia
- Zaimplementowano skrypt JavaScript do dynamicznej zmiany tytułu strony w przeglądarce
- Dodano filtr `document_title_parts` do manipulacji częściami tytułu dokumentu
- Zastosowano filtr `the_title` z obsługą kontekstu dla elementów w pętli WordPress

## [1.2.7] - 2025-04-16
### Poprawiono
- Poprawiono tytuł strony potwierdzenia zamówienia (zmiana z "Blog" na "Potwierdzenie zamówienia")
- Dodano funkcję filtrującą tytuły stron dla własnych punktów końcowych

### Techniczne
- Dodano filtry `pre_get_document_title` i `wp_title` dla poprawnego wyświetlania tytułów stron
- Zaimplementowano funkcję `pco_page_title` do obsługi tytułów niestandardowych stron

## [1.2.6] - 2025-04-16
### Poprawiono
- Całkowicie rozwiązano problem z kodowaniem polskich znaków w sekcji "Wybrane opcje" w wiadomościach e-mail
- Poprawiono wyświetlanie specjalnych znaków diakrytycznych w całym procesie zamówienia
- Usprawniono obsługę danych opcji produktu w całym przepływie danych

### Techniczne
- Dodano flagę `JSON_UNESCAPED_UNICODE` do wszystkich funkcji `json_encode()` w całym kodzie
- Zaimplementowano specjalną logikę dekodującą kody Unicode w funkcji wysyłania wiadomości e-mail
- Poprawiono przetwarzanie danych opcji w funkcji `process_form_submission`
- Dodano rozszerzone debugowanie dla śledzenia przepływu danych i kodowania znaków
- Zoptymalizowano przepływ danych, aby uniknąć zbędnego kodowania i dekodowania

## [1.2.5] - 2025-04-15
### Poprawiono
- Rozwiązano problem z kodowaniem polskich znaków w wiadomościach e-mail
- Ulepszono obsługę danych JSON dla zapewnienia poprawnego kodowania UTF-8
- Zaktualizowano style przycisków i formularzy zgodnie z nowymi wytycznymi projektu
- Zmieniono klasę przycisku na stronie potwierdzenia na `pap-btn-primary`

### Techniczne
- Przebudowano funkcję wysyłania wiadomości e-mail do formatu HTML
- Dodano nagłówki Content-Type z kodowaniem UTF-8 do wiadomości e-mail
- Dodano lepszą walidację danych w funkcjach przetwarzających dane JSON
- Dodano dodatkowe debugowanie dla śledzenia problemów z kodowaniem znaków
- Zaktualizowano sposób przetwarzania danych opcji w JavaScript i PHP

## [1.2.4] - 2025-04-15
### Poprawiono
- Dodano polskie etykiety pól w wiadomościach e-mail
- Poprawiono formatowanie wiadomości e-mail dla lepszej czytelności
- Zaktualizowano sposób wyświetlania danych klienta w wiadomościach e-mail

### Techniczne
- Dodano mapowanie kluczy technicznych na polskie etykiety
- Poprawiono obsługę kodowania znaków w danych klienta
- Dodano pomijanie pól technicznych w wiadomościach e-mail

## [1.2.3] - 2025-04-14
### Poprawiono
- Naprawiono błędy w stylach CSS dla formularza zamówienia
- Poprawiono responsywność formularza na urządzeniach mobilnych
- Zaktualizowano teksty komunikatów dla użytkownika

### Techniczne
- Zoptymalizowano kod CSS dla lepszej wydajności
- Zaktualizowano strukturę HTML formularza dla lepszej dostępności
- Poprawiono obsługę błędów w funkcjach AJAX

## [1.2.2] - 2025-03-29
### Poprawiono
- Naprawiono problem z błędem "Brak danych formularza" podczas przesyłania formularza zamówienia
- Naprawiono problem z błędem "Brak identyfikatora produktu" w formularzu zamówienia
- Ulepszono walidację formularza zamówienia poprzez dodanie sprawdzania wymaganych pól

### Techniczne
- Zmieniono sposób przesyłania danych formularza z FormData na JSON
- Dodano ukryte pola z danymi produktu do formularza zamówienia
- Dodano debugowanie otrzymanych danych formularza
- Zaktualizowano obsługę plików załączanych do formularza

## [1.2.1] - 2025-03-28
### Poprawiono
- Naprawiono problem z wyświetlaniem pełnych nazw opcji w podsumowaniu zamówienia dla pól typu select (pojedynczy wybór)
- Ulepszono sposób przekazywania danych opcji z formularza produktu do formularza zamówienia

### Techniczne
- Zmieniono format danych opcji dla pól typu select - teraz przekazywany jest bezpośrednio ciąg znaków z pełną nazwą opcji
- Dodano dodatkowe debugowanie, aby ułatwić rozwiązywanie problemów
- Zaktualizowano kod wyświetlania opcji w podsumowaniu zamówienia i wiadomości e-mail

## [1.0.4] - 2025-03-28
### Poprawiono
- Naprawiono problem z wyświetlaniem pełnej nazwy opcji w podsumowaniu zamówienia dla pól typu select
- Zaktualizowano sposób pobierania nazw opcji w JavaScript, aby używać tekstu wyświetlanego użytkownikowi zamiast wartości
- Usunięto informację o cenie z tekstu opcji wyświetlanego w podsumowaniu
- Poprawiono przekazywanie pełnych nazw opcji z formularza produktu do formularza zamówienia

### Techniczne
- Zmodyfikowano funkcję collectOptions() w JavaScript, aby poprawnie pobierała pełną nazwę opcji bez informacji o cenie
- Zaktualizowano generowanie opcji select w formularzu produktu, aby używać pełnych nazw opcji jako wartości
- Dodano pole value do struktury opcji, aby zachować oryginalną wartość opcji
- Zaktualizowano komentarze w kodzie dla lepszej czytelności
- Dodano debugowanie struktury danych opcji w formularzu zamówienia

## [1.0.3] - 2025-03-27
### Poprawiono
- Naprawiono problem z przekierowaniem dla niezalogowanych użytkowników (tryb incognito)
- Ulepszono obsługę sesji WooCommerce dla wszystkich typów użytkowników

### Dodano
- Rozszerzone debugowanie dla śledzenia problemów z sesją i przekierowaniami
- Automatyczną inicjalizację sesji dla niezalogowanych użytkowników

### Techniczne
- Dodano automatyczne odświeżanie reguł przepisywania URL w trybie debugowania
- Ulepszono obsługę punktów końcowych dla stron niestandardowych
- Dodano szczegółowe logowanie w kluczowych miejscach kodu

## [1.0.2] - 2025-03-27
### Poprawiono
- Zmieniono układ opcji produktu na akordeonowy z siatką, co poprawia czytelność i skraca długość strony

### Dodano
- Układ akordeonowy dla kategorii opcji z możliwością rozwijania/zwijania
- Responsywną siatkę dla opcji z różną liczbą kolumn w zależności od szerokości ekranu
- Animacje dla akordeonu
- Ulepszony wygląd opcji z tłami, obramowaniami i efektami hover

### Techniczne
- Zaimplementowano obsługę JavaScript dla akordeonu
- Dodano media queries dla responsywnej siatki opcji

## [1.0.1] - 2025-03-27
### Poprawiono
- Poprawiono wyświetlanie cen w podsumowaniu, dodając wyraźne etykiety
- Dodano sekcję "Wybrane opcje" w podsumowaniu zamówienia
- Poprawiono stylizację CSS dla lepszej czytelności formularza
- Zmieniono metodę aktualizacji cen z `.text()` na `.html()` w JavaScript
- Naprawiono problem z wyświetlaniem znaczników HTML w podsumowaniu
- Poprawiono strukturę HTML w sekcji podsumowania dla lepszej czytelności
- Dodano etykiety do wartości cen, aby były bardziej zrozumiałe dla klienta

### Dodano
- Dynamiczna lista wybranych opcji w podsumowaniu
- Nowe style CSS dla sekcji podsumowania
- Funkcja debugowania do śledzenia błędów
- Plik README.md z dokumentacją wtyczki
- Plik CHANGELOG.md do śledzenia zmian
- Dodatkowe efekty wizualne dla sekcji podsumowania (cienie, obramowania)
- Automatyczne ukrywanie sekcji wybranych opcji, gdy nie ma wybranych opcji

### Techniczne
- Zoptymalizowano kod JavaScript dla lepszej wydajności
- Dodano komentarze do kluczowych funkcji
- Poprawiono strukturę kodu CSS dla łatwiejszej konserwacji
- Dodano obsługę błędów w funkcjach AJAX

## [1.0.0] - 2025-03-26
### Dodano
- Pierwsza wersja wtyczki
- Niestandardowy formularz zamówienia dla produktów WooCommerce
- Dynamiczna aktualizacja cen w zależności od wybranych opcji
- Możliwość konfiguracji opcji dla różnych kategorii
- System powiadomień e-mail dla administratora i klienta
- Podstawowe style CSS dla formularza
- Obsługa AJAX dla dynamicznej aktualizacji cen
- Walidacja formularza po stronie serwera
- Zabezpieczenia formularza (nonce)
