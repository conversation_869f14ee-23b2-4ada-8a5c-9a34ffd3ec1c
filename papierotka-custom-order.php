<?php
/**
 * Plugin Name: Papierotka Custom Order
 * Plugin URI: https://papierotka.pl
 * Description: Niestandardowy formularz zamówienia dla produktów WooCommerce
 * Version: 1.3.1
 * Author: <PERSON><PERSON>
 * Author URI: https://papierotka.pl
 * Text Domain: papierotka-custom-order
 * Domain Path: /languages
 * Requires at least: 5.0
 * Requires PHP: 7.2
 * WC requires at least: 3.0
 * WC tested up to: 7.8
 * 
 * @package Papierotka_Custom_Order
 * 
 * Historia zmian:
 * 1.3.1 (2025-04-16) - Ulepszono dynamiczne pobieranie tytułów stron z adresu URL
 * 1.3.0 (2025-04-16) - Naprawiono wyświetlanie tytułów i breadcrumbs na stronie formularza zamówienia
 * 1.2.9 (2025-04-16) - Kompleksowe rozwiązanie problemu z breadcrumbs na stronie potwierdzenia zamówienia
 * 1.2.8 (2025-04-16) - Rozszerzone rozwiązanie problemu z tytułem strony potwierdzenia zamówienia
 * 1.2.7 (2025-04-16) - Poprawiono tytuł strony potwierdzenia zamówienia
 * 1.2.6 (2025-04-16) - Całkowicie rozwiązano problem z polskimi znakami w sekcji "Wybrane opcje"
 * 1.2.5 (2025-04-15) - Rozwiązano problem z kodowaniem polskich znaków w wiadomościach e-mail
 * 1.2.4 (2025-04-15) - Dodano polskie etykiety pól w wiadomościach e-mail
 * 1.2.3 (2025-04-14) - Poprawiono style CSS i responsywność formularza
 * 1.0.2 (2025-03-27) - Zmieniono układ opcji produktu na akordeonowy z siatką
 * 1.0.1 (2025-03-27) - Poprawiono wyświetlanie cen w podsumowaniu, dodano sekcję "Wybrane opcje"
 * 1.0.0 (2025-03-26) - Pierwsza wersja wtyczki
 */

// Zabezpieczenie przed bezpośrednim dostępem do pliku
if (!defined('ABSPATH')) {
    exit;
}

// Definicje stałych
define('PCO_VERSION', '1.3.1');
define('PCO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PCO_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PCO_DEBUG', true); // Włączenie debugowania

// Sprawdzenie, czy WooCommerce jest aktywny
function pco_check_woocommerce() {
    include_once(ABSPATH . 'wp-admin/includes/plugin.php');
    if (!is_plugin_active('woocommerce/woocommerce.php')) {
        add_action('admin_notices', 'pco_woocommerce_notice');
        return false;
    }
    return true;
}

// Komunikat o braku WooCommerce
function pco_woocommerce_notice() {
    ?>
    <div class="error">
        <p><?php _e('Wtyczka Papierotka Custom Order wymaga aktywnego WooCommerce!', 'papierotka-custom-order'); ?></p>
    </div>
    <?php
}

// Funkcja debugowania
function pco_log($message) {
    if (defined('PCO_DEBUG') && PCO_DEBUG) {
        if (is_array($message) || is_object($message)) {
            error_log(print_r($message, true));
        } else {
            error_log($message);
        }
    }
}

// Ładowanie plików wtyczki
function pco_init() {
    if (!pco_check_woocommerce()) {
        return;
    }
    
    require_once PCO_PLUGIN_DIR . 'includes/class-product-fields.php';
    require_once PCO_PLUGIN_DIR . 'includes/class-direct-order-form.php';
    
    // Inicjalizacja klas
    new PCO_Product_Fields();
    new PCO_Direct_Order_Form();
}
add_action('plugins_loaded', 'pco_init');

// Rejestracja skryptów i stylów
function pco_enqueue_scripts() {
    wp_enqueue_style('papierotka-custom-order', PCO_PLUGIN_URL . 'assets/css/papierotka-custom-order.css', array(), PCO_VERSION);
    wp_enqueue_script('papierotka-custom-order', PCO_PLUGIN_URL . 'assets/js/papierotka-custom-order.js', array('jquery'), PCO_VERSION, true);
    
    // Dodanie skryptów naprawiających breadcrumbs
    wp_enqueue_script('pco-breadcrumbs-fix', PCO_PLUGIN_URL . 'assets/js/breadcrumbs-fix.js', array(), PCO_VERSION, true);
    wp_enqueue_script('pco-breadcrumbs-direct-fix', PCO_PLUGIN_URL . 'assets/js/breadcrumbs-direct-fix.js', array(), PCO_VERSION, true);
    
    // Przekazanie danych do skryptu
    wp_localize_script('papierotka-custom-order', 'pco_data', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('pco-ajax-nonce')
    ));
}
add_action('wp_enqueue_scripts', 'pco_enqueue_scripts');

// Aktywacja wtyczki
function pco_activate() {
    // Utworzenie stron
    pco_register_pages();
    
    // Odświeżenie przepisywania URL
    flush_rewrite_rules();
}
register_activation_hook(__FILE__, 'pco_activate');

// Deaktywacja wtyczki
function pco_deactivate() {
    // Odświeżenie przepisywania URL
    flush_rewrite_rules();
}
register_deactivation_hook(__FILE__, 'pco_deactivate');

// Rejestracja własnych stron
function pco_register_pages() {
    // Debugowanie
    pco_log('Rejestracja punktów końcowych i reguł przepisywania URL');
    
    // Rejestracja punktów końcowych
    add_rewrite_endpoint('zamowienie', EP_ROOT);
    add_rewrite_endpoint('potwierdzenie-zamowienia', EP_ROOT);
    
    // Dodanie reguł przepisywania URL
    add_rewrite_rule('^zamowienie/?$', 'index.php?zamowienie=1', 'top');
    add_rewrite_rule('^potwierdzenie-zamowienia/?$', 'index.php?potwierdzenie-zamowienia=1', 'top');
    
    // Odświeżenie reguł przepisywania przy każdym ładowaniu wtyczki (tylko w trybie debugowania)
    if (defined('PCO_DEBUG') && PCO_DEBUG) {
        pco_log('Odświeżenie reguł przepisywania URL');
        flush_rewrite_rules();
    }
}
add_action('init', 'pco_register_pages');

// Obsługa szablonów dla własnych stron
function pco_template_include($template) {
    // Debugowanie
    pco_log('Template include - sprawdzanie punktów końcowych');
    pco_log('Query var zamowienie: ' . (get_query_var('zamowienie', false) ? 'true' : 'false'));
    pco_log('Query var potwierdzenie-zamowienia: ' . (get_query_var('potwierdzenie-zamowienia', false) ? 'true' : 'false'));
    
    if (get_query_var('zamowienie', false)) {
        pco_log('Ładowanie szablonu formularza zamówienia');
        return PCO_PLUGIN_DIR . 'templates/order-form-page.php';
    }
    
    if (get_query_var('potwierdzenie-zamowienia', false)) {
        pco_log('Ładowanie szablonu potwierdzenia zamówienia');
        return PCO_PLUGIN_DIR . 'templates/order-confirmation-page.php';
    }
    
    return $template;
}
add_filter('template_include', 'pco_template_include');

// Zmiana tytułu strony dla własnych stron
function pco_page_title($title) {
    // Sprawdzenie, czy jesteśmy na stronie zamówienia
    if (get_query_var('zamowienie', false)) {
        return __('Zamówienie', 'papierotka-custom-order') . ' | ' . get_bloginfo('name');
    }
    
    // Sprawdzenie, czy jesteśmy na stronie potwierdzenia zamówienia
    if (get_query_var('potwierdzenie-zamowienia', false)) {
        return __('Potwierdzenie zamówienia', 'papierotka-custom-order') . ' | ' . get_bloginfo('name');
    }
    
    return $title;
}
add_filter('pre_get_document_title', 'pco_page_title');
add_filter('wp_title', 'pco_page_title');

// Zmiana tytułu w breadcrumbs i nawigacji
function pco_breadcrumb_title_fix($states) {
    // Sprawdzenie, czy jesteśmy na stronie potwierdzenia zamówienia
    if (get_query_var('potwierdzenie-zamowienia', false)) {
        $page_title = __('Potwierdzenie zamówienia', 'papierotka-custom-order');
        return pco_replace_blog_in_breadcrumbs($states, $page_title);
    }
    
    // Sprawdzenie, czy jesteśmy na stronie zamówienia
    if (get_query_var('zamowienie', false)) {
        $page_title = __('Zamówienie', 'papierotka-custom-order');
        return pco_replace_blog_in_breadcrumbs($states, $page_title);
    }
    
    return $states;
}

// Pomocnicza funkcja do zamiany "Blog" na właściwy tytuł w breadcrumbs
function pco_replace_blog_in_breadcrumbs($states, $page_title) {
    // Sprawdzenie, czy to nie jest prawdziwa strona bloga
    $blog_page_id = get_option('page_for_posts');
    $blog_page = get_post($blog_page_id);
    $blog_title = $blog_page ? $blog_page->post_title : 'Blog';
    
    // Szukamy "Blog" w tablicy stanów i zamieniamy na właściwy tytuł
    foreach ($states as $key => $state) {
        // Sprawdzamy, czy element jest stringiem i czy to "Blog"
        if (is_string($state) && $state === 'Blog') {
            // Sprawdzamy, czy to nie jest link do prawdziwej strony bloga
            $is_blog_link = false;
            if (is_array($states[$key]) && isset($states[$key]['url'])) {
                $blog_url = get_permalink($blog_page_id);
                if ($states[$key]['url'] === $blog_url) {
                    $is_blog_link = true;
                }
            }
            
            if (!$is_blog_link) {
                $states[$key] = $page_title;
            }
        } elseif (is_array($state) && isset($state['text']) && $state['text'] === 'Blog') {
            // Sprawdzamy, czy to nie jest link do prawdziwej strony bloga
            $is_blog_link = false;
            if (isset($state['url'])) {
                $blog_url = get_permalink($blog_page_id);
                if ($state['url'] === $blog_url) {
                    $is_blog_link = true;
                }
            }
            
            if (!$is_blog_link) {
                $states[$key]['text'] = $page_title;
            }
        }
    }
    
    return $states;
}
// Dodanie filtrów dla popularnych wtyczek breadcrumbs
add_filter('woocommerce_get_breadcrumb', 'pco_breadcrumb_title_fix', 20, 1);
add_filter('wpseo_breadcrumb_links', 'pco_breadcrumb_title_fix', 20, 1);
add_filter('rank_math/frontend/breadcrumb/items', 'pco_breadcrumb_title_fix', 20, 1);
add_filter('bcn_breadcrumb_title', 'pco_breadcrumb_title_fix', 20, 1);

// Zmiana tytułu w nagłówku strony (dla motywów, które używają get_the_title)
function pco_the_title($title, $id = null) {
    if (!is_admin() && in_the_loop() && get_query_var('potwierdzenie-zamowienia', false)) {
        return __('Potwierdzenie zamówienia', 'papierotka-custom-order');
    }
    
    if (!is_admin() && in_the_loop() && get_query_var('zamowienie', false)) {
        return __('Zamówienie', 'papierotka-custom-order');
    }
    
    return $title;
}
add_filter('the_title', 'pco_the_title', 10, 2);

// Bezpośrednia zmiana tytułu w breadcrumbs i innych miejscach
function pco_modify_title_parts($title_parts) {
    if (get_query_var('potwierdzenie-zamowienia', false)) {
        $title_parts['title'] = __('Potwierdzenie zamówienia', 'papierotka-custom-order');
    }
    
    if (get_query_var('zamowienie', false)) {
        $title_parts['title'] = __('Zamówienie', 'papierotka-custom-order');
    }
    
    return $title_parts;
}
add_filter('document_title_parts', 'pco_modify_title_parts');