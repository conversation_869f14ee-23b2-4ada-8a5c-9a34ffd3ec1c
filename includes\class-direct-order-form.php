<?php
/**
 * Klasa obsługująca niestandardowy formularz zamówienia
 */
class PCO_Direct_Order_Form {
    
    /**
     * Konstruktor
     */
    public function __construct() {
        // Modyfikacja przycisku dodawania do koszyka
        add_filter('woocommerce_product_single_add_to_cart_text', array($this, 'modify_add_to_cart_text'), 10, 2);
        
        // Przechwycenie akcji dodawania do koszyka
        add_filter('woocommerce_add_to_cart_validation', array($this, 'intercept_add_to_cart'), 10, 5);
        
        // Dodanie nagłówków UTF-8 do maili
        add_filter('wp_mail_content_type', array($this, 'set_mail_content_type'));
        
        // Obsługa przesyłania formularza
        add_action('wp_ajax_pco_submit_form', array($this, 'process_form_submission'));
        add_action('wp_ajax_nopriv_pco_submit_form', array($this, 'process_form_submission'));
        
        // Dodanie danych zamówienia do formularza
        add_action('pco_before_order_form', array($this, 'add_order_summary'));
    }
    
    /**
     * Modyfikacja tekstu przycisku "Dodaj do koszyka"
     */
    public function modify_add_to_cart_text($text, $product) {
        // Sprawdzenie czy niestandardowy formularz jest włączony dla tego produktu
        $enable_custom_form = get_post_meta($product->get_id(), '_pco_enable_custom_form', true);
        if ($enable_custom_form === 'yes') {
            return __('Złóż zamówienie', 'papierotka-custom-order');
        }
        
        return $text;
    }
    
    /**
     * Przechwycenie akcji dodawania do koszyka
     */
    public function intercept_add_to_cart($valid, $product_id, $quantity, $variation_id = 0, $variations = array()) {
        // Sprawdzenie czy to jest niestandardowe zamówienie
        if (isset($_POST['pco_redirect_to_form']) && $_POST['pco_redirect_to_form'] == 1) {
            // Sprawdzenie czy niestandardowy formularz jest włączony dla tego produktu
            $enable_custom_form = get_post_meta($product_id, '_pco_enable_custom_form', true);
            if ($enable_custom_form === 'yes') {
                // Debugowanie - wyświetlenie podstawowych informacji
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    pco_log('Przechwycenie zamówienia - przed zapisem sesji');
                    pco_log('User logged in: ' . (is_user_logged_in() ? 'Yes' : 'No'));
                    pco_log('Session exists: ' . (WC()->session ? 'Yes' : 'No'));
                }
                
                // Upewnienie się, że sesja WooCommerce jest inicjalizowana
                if (!WC()->session->has_session()) {
                    WC()->session->set_customer_session_cookie(true);
                }
                
                // Zapisanie danych w sesji
                WC()->session->set('pco_product_id', $product_id);
                WC()->session->set('pco_quantity', $quantity);
                
                // Debugowanie - wyświetlenie podstawowych informacji
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    pco_log('Dane zapisane w sesji: product_id=' . $product_id . ', quantity=' . $quantity);
                }
                
                // Poprawna obsługa kodowania UTF-8 dla danych opcji
                $options_data = array();
                if (isset($_POST['pco_options_data'])) {
                    $json_data = stripslashes($_POST['pco_options_data']);
                    $options_data = json_decode($json_data, true);
                    
                    // Debugowanie
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        pco_log('Dane opcji przed zapisem w sesji:');
                        pco_log(print_r($options_data, true));
                    }
                }
                
                WC()->session->set('pco_options_data', $options_data);
                WC()->session->set('pco_total_price', isset($_POST['pco_total_price']) ? floatval($_POST['pco_total_price']) : 0);
                
                // Przekierowanie do formularza zamówienia
                $redirect_url = site_url('/zamowienie/');
                wp_redirect($redirect_url);
                exit;
            }
        }
        
        return $valid;
    }
    
    /**
     * Dodanie podsumowania zamówienia do formularza
     */
    public function add_order_summary() {
        // Pobranie danych z sesji
        $product_id = WC()->session->get('pco_product_id');
        $quantity = WC()->session->get('pco_quantity');
        $options_data = WC()->session->get('pco_options_data');
        $total_price = WC()->session->get('pco_total_price');
        
        // Debugowanie - wyświetlenie struktury danych opcji
        if (defined('WP_DEBUG') && WP_DEBUG) {
            pco_log('Struktura danych opcji w formularzu zamówienia:');
            pco_log(print_r($options_data, true));
        }
        
        if (!$product_id) {
            return;
        }
        
        $product = wc_get_product($product_id);
        if (!$product) {
            return;
        }
        
        // Wyświetlenie podsumowania zamówienia
        ?>
        <div class="pco-order-summary">
            <h2><?php _e('Podsumowanie zamówienia', 'papierotka-custom-order'); ?></h2>
            
            <div class="pco-summary-product">
                <h3><?php echo esc_html($product->get_name()); ?></h3>
                <p class="pco-summary-image"><?php echo $product->get_image('thumbnail'); ?></p>
            </div>
            
            <div class="pco-summary-details">
                <p><strong><?php _e('Ilość:', 'papierotka-custom-order'); ?></strong> <?php echo esc_html($quantity); ?></p>
                
                <?php if (!empty($options_data)) : ?>
                <div class="pco-summary-options">
                    <p><strong><?php _e('Wybrane opcje:', 'papierotka-custom-order'); ?></strong></p>
                    <ul>
                        <?php foreach ($options_data as $category => $options) : ?>
                            <li>
                                <strong><?php echo esc_html($this->get_category_name($category)); ?>:</strong>
                                <?php
                                if (is_array($options)) {
                                    // Dla multiselect
                                    $option_names = array_map(function($option) {
                                        return $option['name'];
                                    }, $options);
                                    echo esc_html(implode(', ', $option_names));
                                } else {
                                    // Dla pojedynczego wyboru - teraz options to bezpośrednio ciąg znaków
                                    echo esc_html($options);
                                }
                                ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <div class="pco-summary-price">
                    <p><strong><?php _e('Cena za 1 zaproszenie:', 'papierotka-custom-order'); ?></strong> <?php echo wc_price($total_price); ?></p>
                    <p><strong><?php _e('Cena łączna:', 'papierotka-custom-order'); ?></strong> <?php echo wc_price($total_price * $quantity); ?></p>
                </div>
            </div>
            
            <input type="hidden" name="pco_product_id" value="<?php echo esc_attr($product_id); ?>">
            <input type="hidden" name="pco_quantity" value="<?php echo esc_attr($quantity); ?>">
            <input type="hidden" name="pco_options_data" value='<?php echo esc_attr(json_encode($options_data, JSON_UNESCAPED_UNICODE)); ?>'>
            <input type="hidden" name="pco_total_price" value="<?php echo esc_attr($total_price); ?>">
        </div>
        <?php
    }
    
    /**
     * Obsługa przesyłania formularza
     */
    public function process_form_submission() {
        check_ajax_referer('pco-ajax-nonce', 'nonce');
        
        // Pobranie danych formularza
        $form_data = isset($_POST['form_data']) ? json_decode(stripslashes($_POST['form_data']), true) : array();
        
        // Debugowanie - wyświetlenie otrzymanych danych
        if (defined('WP_DEBUG') && WP_DEBUG) {
            pco_log('Otrzymane dane formularza:');
            pco_log(print_r($form_data, true));
        }
        
        if (empty($form_data)) {
            wp_send_json_error(array('message' => __('Brak danych formularza', 'papierotka-custom-order')));
            return;
        }
        
        // Sprawdzenie czy dane produktu są dostępne
        if (!isset($form_data['pco_product_id']) || intval($form_data['pco_product_id']) <= 0) {
            wp_send_json_error(array('message' => __('Brak identyfikatora produktu', 'papierotka-custom-order')));
            return;
        }
        
        // Sprawdzenie wymaganych pól
        $required_fields = array('name', 'email', 'phone', 'bride_name', 'groom_name', 'wedding_date', 'wedding_time');
        foreach ($required_fields as $field) {
            if (empty($form_data[$field])) {
                wp_send_json_error(array('message' => sprintf(__('Pole %s jest wymagane', 'papierotka-custom-order'), $field)));
                return;
            }
        }
        
        // Przetwarzanie danych opcji - upewnienie się, że polskie znaki są poprawnie obsługiwane
        if (isset($form_data['pco_options_data']) && !is_array($form_data['pco_options_data'])) {
            $form_data['pco_options_data'] = json_decode($form_data['pco_options_data'], true);
            
            // Debugowanie - wyświetlenie zdekodowanych danych opcji
            if (defined('WP_DEBUG') && WP_DEBUG) {
                pco_log('Zdekodowane dane opcji:');
                pco_log(print_r($form_data['pco_options_data'], true));
            }
        }
        
        // Pobranie danych produktu
        $product_id = isset($form_data['pco_product_id']) ? intval($form_data['pco_product_id']) : 0;
        $quantity = isset($form_data['pco_quantity']) ? intval($form_data['pco_quantity']) : 1;
        $options_data = isset($form_data['pco_options_data']) ? $form_data['pco_options_data'] : array();
        $total_price = isset($form_data['pco_total_price']) ? floatval($form_data['pco_total_price']) : 0;
        
        if (!$product_id) {
            wp_send_json_error(array('message' => __('Brak identyfikatora produktu', 'papierotka-custom-order')));
            return;
        }
        
        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(array('message' => __('Produkt nie istnieje', 'papierotka-custom-order')));
            return;
        }
        
        // Generowanie unikalnego ID zamówienia
        $order_id = 'PCO-' . date('Ymd') . '-' . substr(uniqid(), -5);
        
        // Zapisanie zamówienia w bazie danych
        $this->save_order_to_database($order_id, $form_data);
        
        // Wysłanie powiadomienia e-mail
        $this->send_order_notification($order_id, $form_data);
        
        // Wyczyszczenie sesji
        WC()->session->set('pco_product_id', null);
        WC()->session->set('pco_quantity', null);
        WC()->session->set('pco_options_data', null);
        WC()->session->set('pco_total_price', null);
        
        // Zwrócenie odpowiedzi
        wp_send_json_success(array(
            'message' => __('Zamówienie zostało przyjęte', 'papierotka-custom-order'),
            'redirect' => add_query_arg('order_id', $order_id, site_url('/potwierdzenie-zamowienia/'))
        ));
    }
    
    /**
     * Zapisanie zamówienia w bazie danych
     */
    private function save_order_to_database($order_id, $form_data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'pco_orders';
        
        // Sprawdzenie czy tabela istnieje, jeśli nie - utworzenie jej
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            $this->create_orders_table();
        }
        
        // Zapisanie zamówienia
        $wpdb->insert(
            $table_name,
            array(
                'order_id' => $order_id,
                'product_id' => isset($form_data['pco_product_id']) ? intval($form_data['pco_product_id']) : 0,
                'quantity' => isset($form_data['pco_quantity']) ? intval($form_data['pco_quantity']) : 1,
                'options_data' => isset($form_data['pco_options_data']) ? (is_array($form_data['pco_options_data']) ? json_encode($form_data['pco_options_data'], JSON_UNESCAPED_UNICODE) : $form_data['pco_options_data']) : '',
                'total_price' => isset($form_data['pco_total_price']) ? floatval($form_data['pco_total_price']) : 0,
                'form_data' => json_encode($form_data, JSON_UNESCAPED_UNICODE),
                'created_at' => current_time('mysql')
            )
        );
        
        return $wpdb->insert_id;
    }
    
    /**
     * Utworzenie tabeli zamówień
     */
    private function create_orders_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'pco_orders';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            order_id varchar(20) NOT NULL,
            product_id bigint(20) NOT NULL,
            quantity int(11) NOT NULL,
            options_data text NOT NULL,
            total_price decimal(10,2) NOT NULL,
            form_data text NOT NULL,
            created_at datetime NOT NULL,
            PRIMARY KEY  (id),
            UNIQUE KEY order_id (order_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Wysłanie powiadomienia e-mail
     */
    private function send_order_notification($order_id, $form_data) {
        $admin_email = get_option('admin_email');
        $customer_email = isset($form_data['email']) ? sanitize_email($form_data['email']) : '';
        
        if (empty($customer_email)) {
            return false;
        }
        
        // Pobranie danych produktu
        $product_id = isset($form_data['pco_product_id']) ? intval($form_data['pco_product_id']) : 0;
        $product = wc_get_product($product_id);
        $product_name = $product ? $product->get_name() : __('Produkt', 'papierotka-custom-order');
        
        // Przygotowanie treści wiadomości
        $subject = sprintf(__('Nowe zamówienie #%s', 'papierotka-custom-order'), $order_id);
        
        // Początek HTML
        $message = '<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /></head><body>';
        
        $message .= '<p>' . sprintf(__('Nowe zamówienie #%s zostało złożone.', 'papierotka-custom-order'), $order_id) . '</p>';
        $message .= '<h3>' . __('Szczegóły zamówienia:', 'papierotka-custom-order') . '</h3>';
        $message .= '<p><strong>' . __('Produkt:', 'papierotka-custom-order') . '</strong> ' . $product_name . '</p>';
        $message .= '<p><strong>' . __('Ilość:', 'papierotka-custom-order') . '</strong> ' . (isset($form_data['pco_quantity']) ? intval($form_data['pco_quantity']) : 1) . '</p>';
        
        // Dodanie wybranych opcji
        $options_data = array();
        if (isset($form_data['pco_options_data'])) {
            // Jeśli dane są już tablicą, użyj ich bezpośrednio
            if (is_array($form_data['pco_options_data'])) {
                $options_data = $form_data['pco_options_data'];
            } 
            // Jeśli dane są stringiem JSON, zdekoduj je z zachowaniem polskich znaków
            else {
                $json_data = stripslashes($form_data['pco_options_data']);
                $options_data = json_decode($json_data, true);
            }
            
            // Debugowanie
            if (defined('WP_DEBUG') && WP_DEBUG) {
                pco_log('Dane opcji przed generowaniem wiadomości e-mail:');
                pco_log(print_r($options_data, true));
            }
        }
        
        if (!empty($options_data)) {
            $message .= '<h3>' . __('Wybrane opcje:', 'papierotka-custom-order') . '</h3>';
            $message .= '<ul>';
            
            foreach ($options_data as $category => $options) {
                $category_name = $this->get_category_name($category);
                $message .= '<li><strong>' . $category_name . ':</strong> ';
                
                if (is_array($options)) {
                    // Dla multiselect
                    $option_names = array_map(function($option) {
                        // Upewnij się, że nazwa opcji jest poprawnie zakodowana
                        if (is_array($option) && isset($option['name'])) {
                            return $option['name'];
                        }
                        return $option;
                    }, $options);
                    $message .= implode(', ', $option_names);
                } else {
                    // Dla pojedynczego wyboru - upewnij się, że polskie znaki są poprawnie wyświetlane
                    // Jeśli opcja zawiera kody Unicode, dekoduj je
                    if (preg_match('/u[0-9a-f]{4}/i', $options)) {
                        $decoded_option = json_decode('"' . $options . '"');
                        if ($decoded_option) {
                            $message .= $decoded_option;
                        } else {
                            $message .= $options;
                        }
                    } else {
                        $message .= $options;
                    }
                }
                
                $message .= '</li>';
            }
            $message .= '</ul>';
        }
        
        // Dodanie danych klienta
        $message .= '<h3>' . __('Dane klienta:', 'papierotka-custom-order') . '</h3>';

        // Mapowanie kluczy na polskie etykiety
        $field_labels = [
            'name' => 'Imię i nazwisko',
            'email' => 'E-mail',
            'phone' => 'Telefon',
            'bride_name' => 'Imię Panny Młodej',
            'groom_name' => 'Imię Pana Młodego',
            'wedding_date' => 'Data ślubu',
            'wedding_time' => 'Godzina ślubu',
            'church_name' => 'Nazwa kościoła / USC',
            'church_address' => 'Adres kościoła / USC',
            'reception_name' => 'Nazwa lokalu weselnego',
            'reception_address' => 'Adres lokalu weselnego',
            'reception_time' => 'Godzina przyjęcia',
            'guest_list' => 'Lista gości',
            'additional_info' => 'Dodatkowe informacje',
            '_wp_http_referer' => '', // pole techniczne, pomijamy
        ];

        $message .= '<ul>';
        foreach ($form_data as $key => $value) {
            // Pominięcie pól technicznych
            if (strpos($key, 'pco_') === 0 || $key === '_wp_http_referer') {
                continue;
            }
            if (isset($field_labels[$key]) && $field_labels[$key] !== '') {
                $label = $field_labels[$key];
            } else {
                $label = $key;
            }
            
            // Formatowanie wartości (np. lista gości z podziałem na wiersze)
            if ($key === 'guest_list' || $key === 'additional_info') {
                $value = nl2br($value);
            }
            
            $message .= '<li><strong>' . $label . ':</strong> ' . $value . '</li>';
        }
        $message .= '</ul>';
        
        // Dodanie ceny
        $total_price = isset($form_data['pco_total_price']) ? floatval($form_data['pco_total_price']) : 0;
        $quantity = isset($form_data['pco_quantity']) ? intval($form_data['pco_quantity']) : 1;
        $message .= '<p><strong>' . __('Cena za 1 zaproszenie:', 'papierotka-custom-order') . '</strong> ' . number_format($total_price, 2) . ' zł</p>';
        $message .= '<p><strong>' . __('Cena łączna:', 'papierotka-custom-order') . '</strong> ' . number_format($total_price * $quantity, 2) . ' zł</p>';
        
        // Zakończenie HTML
        $message .= '</body></html>';
        
        // Wysłanie e-maila do administratora
        wp_mail($admin_email, $subject, $message);
        
        // Wysłanie potwierdzenia do klienta
        $customer_subject = sprintf(__('Potwierdzenie zamówienia #%s', 'papierotka-custom-order'), $order_id);
        $customer_message = '<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /></head><body>';
        $customer_message .= '<p>' . sprintf(__('Dziękujemy za złożenie zamówienia #%s.', 'papierotka-custom-order'), $order_id) . '</p>';
        $customer_message .= '<p>' . __('Twoje zamówienie zostało przyjęte do realizacji. Skontaktujemy się z Tobą wkrótce.', 'papierotka-custom-order') . '</p>';
        $customer_message .= '<hr>';
        $customer_message .= $message;
        $customer_message .= '</body></html>';
        
        wp_mail($customer_email, $customer_subject, $customer_message);
        
        return true;
    }
    
    /**
     * Pobieranie nazwy kategorii
     */
    private function get_category_name($category_id) {
        $categories = array(
            'koperta' => __('Koperta', 'papierotka-custom-order'),
            'personalizacja_koperty' => __('Personalizacja koperty', 'papierotka-custom-order'),
            'lak' => __('Lak', 'papierotka-custom-order'),
            'karty_zaproszenia' => __('Karty zaproszenia', 'papierotka-custom-order'),
            'zlocenia' => __('Złocenia', 'papierotka-custom-order'),
            'wstazka_sznurek' => __('Wstążka / Sznurek', 'papierotka-custom-order'),
            'opaska' => __('Opaska', 'papierotka-custom-order'),
            'dodatki' => __('Dodatki', 'papierotka-custom-order')
        );
        
        return isset($categories[$category_id]) ? $categories[$category_id] : $category_id;
    }
    
    /**
     * Ustawienie typu zawartości maila jako HTML z kodowaniem UTF-8
     */
    public function set_mail_content_type() {
        return 'text/html; charset=UTF-8';
    }
}