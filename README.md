# Papierotka Custom Order

Wtyczka WordPress do obsługi niestandardowych zamówień zaproszeń ślubnych dla sklepu WooCommerce.

## Opis

Wtyczka Papierotka Custom Order umożliwia klientom personalizację zaproszeń ślubnych poprzez wybór różnych opcji, takich jak:
- Rod<PERSON>j koperty
- Personalizacja koperty
- Dodatki do zaproszeń
- i wiele innych

Wtyczka dynamicznie aktualizuje cenę w zależności od wybranych opcji i umożliwia złożenie zamówienia bez standardowego procesu zakupowego WooCommerce.

## Instalacja

1. Skopiuj folder `papierotka-custom-order` do katalogu `/wp-content/plugins/` na serwerze WordPress
2. Aktywuj wtyczkę w panelu administracyjnym WordPress (Wtyczki → Zainstalowane wtyczki)
3. Przejdź do Ustawienia → Bezpośrednie linki i kliknij "Zapisz zmiany", aby odświeżyć przepisywanie URL

## Konfiguracja

1. Edytuj produkt w WooCommerce, dla którego chcesz włączyć niestandardowy formularz
2. W sekcji danych produktu zaznacz opcję "Włącz niestandardowy formularz zamówienia"
3. Dodaj kategorie opcji, które chcesz udostępnić klientom
4. Dla każdej kategorii dodaj opcje w formacie "Nazwa opcji | Cena"
5. Zapisz produkt

### Szczegółowa konfiguracja kategorii opcji

Wtyczka obsługuje następujące kategorie opcji:

| ID Kategorii | Nazwa wyświetlana | Opis |
|--------------|-------------------|------|
| koperta | Koperta | Rodzaj koperty do zaproszenia |
| personalizacja_koperty | Personalizacja koperty | Opcje personalizacji koperty |
| lak | Lak | Rodzaj laku do pieczęci |
| karty_zaproszenia | Karty zaproszenia | Rodzaje kart zaproszenia |
| zlocenia | Złocenia | Opcje złoceń na zaproszeniu |
| wstazka_sznurek | Wstążka / Sznurek | Rodzaje wstążek lub sznurków |
| opaska | Opaska | Rodzaje opasek do zaproszeń |
| dodatki | Dodatki | Dodatkowe elementy zaproszenia |

Dla każdej kategorii można dodać wiele opcji w formacie:
```
Nazwa opcji | Cena
```

Na przykład:
```
Biała | 5.00
Kremowa | 7.50
Złota | 10.00
```

## Struktura plików

- `papierotka-custom-order.php` - Główny plik wtyczki
- `includes/`
  - `class-product-fields.php` - Zarządza polami produktu i wyświetlaniem opcji
  - `class-direct-order-form.php` - Obsługuje formularz zamówienia
- `assets/`
  - `js/papierotka-custom-order.js` - Obsługuje dynamiczną aktualizację cen
  - `css/papierotka-custom-order.css` - Style dla formularza
- `templates/`
  - `order-form-page.php` - Szablon strony formularza zamówienia
  - `order-confirmation-page.php` - Szablon strony potwierdzenia zamówienia

## Szczegółowy opis funkcjonalności

### 1. Dynamiczna aktualizacja cen

Wtyczka automatycznie aktualizuje ceny w czasie rzeczywistym, gdy klient:
- Zmienia ilość zamawianych zaproszeń
- Wybiera różne opcje personalizacji
- Dodaje lub usuwa dodatki

Aktualizacja cen odbywa się poprzez AJAX, bez przeładowania strony, co zapewnia płynne doświadczenie użytkownika.

### 2. Niestandardowy formularz zamówienia

Po kliknięciu przycisku "Złóż zamówienie", klient jest przekierowywany do niestandardowego formularza, gdzie może podać:
- Dane osobowe (imię, nazwisko, email, telefon)
- Szczegóły ślubu (data, miejsce)
- Dodatkowe informacje
- Załączyć listę gości

### 3. Powiadomienia email

Po złożeniu zamówienia, wtyczka automatycznie wysyła powiadomienia:
- Do administratora sklepu - zawierające wszystkie szczegóły zamówienia
- Do klienta - potwierdzenie przyjęcia zamówienia

#### Edycja szablonów e-mail

Od wersji 1.4.0 dostępna jest możliwość edycji szablonów e-mail w panelu administracyjnym:

1. Przejdź do **WooCommerce → Powiadomienia E-mail**
2. Edytuj szablony dla administratora i klienta
3. Używaj zmiennych dynamicznych takich jak:
   - `{order_id}` - numer zamówienia
   - `{customer_name}` - imię i nazwisko klienta
   - `{product_name}` - nazwa produktu
   - `{total_price}` - cena łączna
   - `{wedding_date}` - data ślubu
   - i wiele innych...
4. Podglądaj e-maile przed zapisaniem
5. Włączaj/wyłączaj poszczególne typy powiadomień

### 4. Bezpieczeństwo

Wtyczka implementuje następujące mechanizmy bezpieczeństwa:
- Weryfikacja nonce dla wszystkich formularzy
- Sanityzacja danych wejściowych
- Walidacja formularzy po stronie serwera

## Debugowanie

Wtyczka zawiera funkcję debugowania, którą można włączyć, ustawiając stałą `PCO_DEBUG` na `true` w pliku głównym wtyczki.

```php
define('PCO_DEBUG', true);
```

Logi debugowania są zapisywane w pliku `debug.log` w katalogu WordPress.

### Typowe problemy i rozwiązania

1. **Problem**: Ceny nie aktualizują się dynamicznie
   **Rozwiązanie**: Sprawdź, czy skrypt JavaScript jest poprawnie ładowany i czy nie ma błędów w konsoli przeglądarki.

2. **Problem**: Formularz zamówienia nie wyświetla się
   **Rozwiązanie**: Odśwież przepisywanie URL w ustawieniach WordPress (Ustawienia → Bezpośrednie linki).

3. **Problem**: Powiadomienia email nie są wysyłane
   **Rozwiązanie**: Sprawdź konfigurację SMTP w WordPress lub zainstaluj wtyczkę do obsługi email (np. WP Mail SMTP).

4. **Problem**: Przekierowanie do formularza zamówienia nie działa w trybie incognito
   **Rozwiązanie**: Upewnij się, że sesja WooCommerce jest poprawnie inicjalizowana. Od wersji 1.0.3 wtyczka automatycznie inicjalizuje sesję dla niezalogowanych użytkowników.

### Obsługa sesji i przekierowań

Od wersji 1.0.3 wtyczka zawiera ulepszoną obsługę sesji WooCommerce dla niezalogowanych użytkowników:

1. **Automatyczna inicjalizacja sesji** - Wtyczka automatycznie inicjalizuje sesję WooCommerce dla niezalogowanych użytkowników przed zapisem danych.
2. **Rozszerzone debugowanie** - W trybie debugowania zapisywane są szczegółowe informacje o stanie sesji i przekierowaniach.
3. **Automatyczne odświeżanie reguł przepisywania URL** - W trybie debugowania reguły przepisywania URL są automatycznie odświeżane przy każdym ładowaniu wtyczki.

## Rozszerzanie wtyczki

### Dodawanie nowych kategorii opcji

1. Dodaj nową kategorię w tablicy `$categories` w metodzie `get_category_name()` w pliku `class-direct-order-form.php`:

```php
private function get_category_name($category_id) {
    $categories = array(
        'koperta' => __('Koperta', 'papierotka-custom-order'),
        // Dodaj nową kategorię
        'nowa_kategoria' => __('Nazwa nowej kategorii', 'papierotka-custom-order'),
    );

    return isset($categories[$category_id]) ? $categories[$category_id] : $category_id;
}
```

2. Dodaj nową kategorię w skrypcie JavaScript w funkcji `updateSelectedOptionsList()` w pliku `assets/js/papierotka-custom-order.js`:

```javascript
var categoryNames = {
    'koperta': 'Koperta',
    // Dodaj nową kategorię
    'nowa_kategoria': 'Nazwa nowej kategorii',
};
```

### Modyfikacja formularza zamówienia

Formularz zamówienia jest generowany w metodzie `generate_form()` w pliku `class-direct-order-form.php`. Aby dodać nowe pola do formularza, zmodyfikuj tę metodę.

## Historia zmian

Zobacz plik [CHANGELOG.md](CHANGELOG.md) dla pełnej historii zmian.

## Wsparcie i rozwój

W przypadku problemów lub pytań dotyczących wtyczki, skontaktuj się z autorem.

## Autor

Papierotka - Zaproszenia ślubne
