/**
 * Form Builder JavaScript
 */

jQuery(document).ready(function($) {
    
    var formConfig = window.pcoFormConfig || { sections: [] };
    var fieldTypes = window.pcoFieldTypes || {};
    var selectedField = null;
    var selectedSection = null;
    
    // Initialize form builder
    init();
    
    function init() {
        setupDragAndDrop();
        renderFormSections();
        bindEvents();
        
        // Show empty state if no sections
        if (!formConfig.sections || formConfig.sections.length === 0) {
            showEmptyCanvas();
        }
    }
    
    function setupDragAndDrop() {
        // Make field types draggable
        $('.pco-field-type').draggable({
            helper: 'clone',
            revert: 'invalid',
            zIndex: 1000,
            start: function(event, ui) {
                ui.helper.addClass('ui-draggable-dragging');
            }
        });
        
        // Make sections sortable
        $('#pco-form-sections').sortable({
            handle: '.pco-section-title',
            placeholder: 'pco-drag-placeholder',
            update: function(event, ui) {
                updateSectionOrder();
            }
        });
    }
    
    function bindEvents() {
        // Add section button
        $('#pco-add-section').on('click', addSection);
        
        // Save form button
        $('#pco-save-form').on('click', saveForm);
        
        // Preview form button
        $('#pco-preview-form').on('click', previewForm);
        
        // Modal close
        $(document).on('click', '.pco-modal-close', function() {
            $('#pco-form-preview-modal').hide();
        });
        
        // Close modal on outside click
        $(document).on('click', '#pco-form-preview-modal', function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        });
        
        // ESC key to close modal
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27) {
                $('#pco-form-preview-modal').hide();
            }
        });
        
        // Property changes
        $(document).on('change', '#pco-field-properties input, #pco-field-properties select, #pco-field-properties textarea', updateFieldProperty);
        
        // Field selection
        $(document).on('click', '.pco-form-field-item', selectField);
        
        // Section selection
        $(document).on('click', '.pco-section-header', selectSection);
        
        // Delete field
        $(document).on('click', '.pco-delete-field', deleteField);
        
        // Delete section
        $(document).on('click', '.pco-delete-section', deleteSection);
        
        // Edit section
        $(document).on('click', '.pco-edit-section', editSection);
    }
    
    function renderFormSections() {
        var $container = $('#pco-form-sections');
        $container.empty();
        
        if (!formConfig.sections || formConfig.sections.length === 0) {
            showEmptyCanvas();
            return;
        }
        
        formConfig.sections.forEach(function(section, sectionIndex) {
            var $section = renderSection(section, sectionIndex);
            $container.append($section);
        });
        
        // Make fields sortable within sections
        $('.pco-form-fields').sortable({
            connectWith: '.pco-form-fields',
            placeholder: 'pco-drag-placeholder',
            update: function(event, ui) {
                updateFieldOrder();
            }
        });
        
        // Make sections droppable for new fields
        $('.pco-form-fields').droppable({
            accept: '.pco-field-type',
            hoverClass: 'pco-drag-over',
            drop: function(event, ui) {
                var fieldType = ui.draggable.data('type');
                var sectionIndex = $(this).closest('.pco-form-section').data('section-index');
                addFieldToSection(fieldType, sectionIndex);
            }
        });
    }
    
    function renderSection(section, sectionIndex) {
        var $section = $('<div class="pco-form-section" data-section-index="' + sectionIndex + '">');
        
        // Section header
        var $header = $('<div class="pco-section-header">');
        $header.append('<h3 class="pco-section-title">' + escapeHtml(section.title || 'Nowa sekcja') + '</h3>');
        
        var $actions = $('<div class="pco-section-actions">');
        $actions.append('<button type="button" class="button pco-edit-section">Edytuj</button>');
        $actions.append('<button type="button" class="button pco-delete-section">Usuń</button>');
        $header.append($actions);
        
        $section.append($header);
        
        // Section description
        if (section.description) {
            $section.append('<p class="pco-section-description">' + escapeHtml(section.description) + '</p>');
        }
        
        // Fields container
        var $fieldsContainer = $('<div class="pco-form-fields">');
        
        if (section.fields && section.fields.length > 0) {
            section.fields.forEach(function(field, fieldIndex) {
                var $field = renderField(field, sectionIndex, fieldIndex);
                $fieldsContainer.append($field);
            });
        } else {
            $fieldsContainer.append('<div class="pco-empty-section">Przeciągnij pola tutaj</div>');
        }
        
        $section.append($fieldsContainer);
        
        return $section;
    }
    
    function renderField(field, sectionIndex, fieldIndex) {
        var fieldType = fieldTypes[field.type] || { label: field.type, icon: 'dashicons-admin-generic' };
        
        var $field = $('<div class="pco-form-field-item" data-section-index="' + sectionIndex + '" data-field-index="' + fieldIndex + '">');
        
        // Field header
        var $header = $('<div class="pco-field-header">');
        $header.append('<span class="pco-field-label">' + escapeHtml(field.label || 'Nowe pole') + '</span>');
        
        var $meta = $('<div>');
        $meta.append('<span class="pco-field-type-badge">' + escapeHtml(fieldType.label) + '</span>');
        
        var $actions = $('<div class="pco-field-actions">');
        $actions.append('<button type="button" class="button pco-delete-field">×</button>');
        $meta.append($actions);
        
        $header.append($meta);
        $field.append($header);
        
        // Field preview
        var preview = generateFieldPreview(field);
        $field.append('<div class="pco-field-preview">' + preview + '</div>');
        
        return $field;
    }
    
    function generateFieldPreview(field) {
        var preview = '';
        var required = field.required ? ' *' : '';
        
        switch (field.type) {
            case 'text':
            case 'email':
            case 'tel':
                preview = 'ID: ' + field.id + required;
                if (field.placeholder) {
                    preview += ' | Placeholder: ' + field.placeholder;
                }
                break;
                
            case 'textarea':
                preview = 'ID: ' + field.id + required;
                if (field.rows) {
                    preview += ' | Wiersze: ' + field.rows;
                }
                break;
                
            case 'select':
            case 'radio':
                preview = 'ID: ' + field.id + required;
                if (field.options && field.options.length > 0) {
                    preview += ' | Opcje: ' + field.options.length;
                }
                break;
                
            case 'checkbox':
                preview = 'ID: ' + field.id + required;
                break;
                
            case 'date':
            case 'time':
                preview = 'ID: ' + field.id + required;
                break;
                
            case 'file':
                preview = 'ID: ' + field.id + required;
                if (field.validation && field.validation.file_types) {
                    preview += ' | Typy: ' + field.validation.file_types.join(', ');
                }
                break;
                
            default:
                preview = 'ID: ' + field.id + required;
        }
        
        return preview;
    }
    
    function showEmptyCanvas() {
        $('#pco-form-sections').html('<div class="pco-empty-canvas">Dodaj sekcję, aby rozpocząć budowanie formularza</div>');
    }
    
    function addSection() {
        var newSection = {
            id: 'section_' + Date.now(),
            title: 'Nowa sekcja',
            description: '',
            fields: []
        };
        
        if (!formConfig.sections) {
            formConfig.sections = [];
        }
        
        formConfig.sections.push(newSection);
        renderFormSections();
        
        // Select the new section for editing
        var sectionIndex = formConfig.sections.length - 1;
        selectSection(null, sectionIndex);
        showSectionProperties(newSection, sectionIndex);
    }
    
    function addFieldToSection(fieldType, sectionIndex) {
        var newField = {
            id: 'field_' + Date.now(),
            type: fieldType,
            label: fieldTypes[fieldType].label,
            placeholder: '',
            required: false,
            validation: {},
            css_class: '',
            help_text: ''
        };
        
        // Add type-specific properties
        if (fieldType === 'textarea') {
            newField.rows = 5;
        } else if (fieldType === 'select' || fieldType === 'radio') {
            newField.options = [];
        } else if (fieldType === 'file') {
            newField.validation = { file_types: [] };
        }
        
        if (!formConfig.sections[sectionIndex].fields) {
            formConfig.sections[sectionIndex].fields = [];
        }
        
        formConfig.sections[sectionIndex].fields.push(newField);
        renderFormSections();
        
        // Select the new field for editing
        var fieldIndex = formConfig.sections[sectionIndex].fields.length - 1;
        selectField(null, sectionIndex, fieldIndex);
    }
    
    function selectField(event, sectionIndex, fieldIndex) {
        if (event) {
            event.stopPropagation();
            sectionIndex = $(event.currentTarget).data('section-index');
            fieldIndex = $(event.currentTarget).data('field-index');
        }
        
        // Remove previous selection
        $('.pco-form-field-item').removeClass('selected');
        
        // Add selection to current field
        $('.pco-form-field-item[data-section-index="' + sectionIndex + '"][data-field-index="' + fieldIndex + '"]').addClass('selected');
        
        selectedField = { sectionIndex: sectionIndex, fieldIndex: fieldIndex };
        selectedSection = null;
        
        var field = formConfig.sections[sectionIndex].fields[fieldIndex];
        showFieldProperties(field, sectionIndex, fieldIndex);
    }
    
    function selectSection(event, sectionIndex) {
        if (event) {
            event.stopPropagation();
            sectionIndex = $(event.currentTarget).closest('.pco-form-section').data('section-index');
        }
        
        // Remove field selection
        $('.pco-form-field-item').removeClass('selected');
        selectedField = null;
        selectedSection = sectionIndex;
        
        var section = formConfig.sections[sectionIndex];
        showSectionProperties(section, sectionIndex);
    }
    
    function showFieldProperties(field, sectionIndex, fieldIndex) {
        var $container = $('#pco-field-properties');
        $container.empty();
        
        $container.append('<h4>Właściwości pola</h4>');
        
        // Basic properties
        $container.append(createPropertyInput('field_id', 'ID pola', field.id, 'text', 'Unikalny identyfikator pola'));
        $container.append(createPropertyInput('field_label', 'Etykieta', field.label, 'text'));
        
        // Type-specific properties
        var fieldTypeConfig = fieldTypes[field.type] || {};
        var supports = fieldTypeConfig.supports || [];
        
        if (supports.includes('placeholder')) {
            $container.append(createPropertyInput('field_placeholder', 'Tekst zastępczy', field.placeholder, 'text'));
        }
        
        if (supports.includes('rows')) {
            $container.append(createPropertyInput('field_rows', 'Liczba wierszy', field.rows, 'number'));
        }
        
        // Required checkbox
        $container.append(createPropertyCheckbox('field_required', 'Pole wymagane', field.required));
        
        if (supports.includes('css_class')) {
            $container.append(createPropertyInput('field_css_class', 'Klasa CSS', field.css_class, 'text'));
        }
        
        if (supports.includes('help_text')) {
            $container.append(createPropertyInput('field_help_text', 'Tekst pomocy', field.help_text, 'textarea'));
        }
        
        // Options for select and radio
        if (supports.includes('options')) {
            $container.append(createOptionsEditor(field.options || []));
        }
        
        // File types for file fields
        if (supports.includes('file_types')) {
            var fileTypes = (field.validation && field.validation.file_types) ? field.validation.file_types.join(', ') : '';
            $container.append(createPropertyInput('field_file_types', 'Dozwolone typy plików', fileTypes, 'text', 'Oddziel przecinkami, np: jpg, png, pdf'));
        }
    }
    
    function showSectionProperties(section, sectionIndex) {
        var $container = $('#pco-field-properties');
        $container.empty();
        
        $container.append('<h4>Właściwości sekcji</h4>');
        
        $container.append(createPropertyInput('section_title', 'Tytuł sekcji', section.title, 'text'));
        $container.append(createPropertyInput('section_description', 'Opis sekcji', section.description, 'textarea'));
    }
    
    function createPropertyInput(id, label, value, type, description) {
        type = type || 'text';
        value = value || '';
        
        var $group = $('<div class="pco-property-group">');
        $group.append('<label for="' + id + '">' + label + '</label>');
        
        if (type === 'textarea') {
            $group.append('<textarea id="' + id + '" rows="3">' + escapeHtml(value) + '</textarea>');
        } else {
            $group.append('<input type="' + type + '" id="' + id + '" value="' + escapeHtml(value) + '">');
        }
        
        if (description) {
            $group.append('<p class="description">' + description + '</p>');
        }
        
        return $group;
    }
    
    function createPropertyCheckbox(id, label, checked) {
        var $group = $('<div class="pco-property-group">');
        var $wrapper = $('<div class="pco-checkbox-wrapper">');
        
        $wrapper.append('<input type="checkbox" id="' + id + '"' + (checked ? ' checked' : '') + '>');
        $wrapper.append('<label for="' + id + '">' + label + '</label>');
        
        $group.append($wrapper);
        
        return $group;
    }
    
    function createOptionsEditor(options) {
        var $group = $('<div class="pco-property-group">');
        $group.append('<label>Opcje</label>');
        
        var $list = $('<div class="pco-options-list" id="pco-options-list">');
        
        options.forEach(function(option, index) {
            $list.append(createOptionItem(option, index));
        });
        
        $group.append($list);
        $group.append('<button type="button" class="button pco-add-option">Dodaj opcję</button>');
        
        return $group;
    }
    
    function createOptionItem(option, index) {
        var $item = $('<div class="pco-option-item">');
        $item.append('<input type="text" placeholder="Etykieta" value="' + escapeHtml(option.label || '') + '" data-property="label">');
        $item.append('<input type="text" placeholder="Wartość" value="' + escapeHtml(option.value || '') + '" data-property="value">');
        $item.append('<button type="button" class="button pco-remove-option">×</button>');
        
        return $item;
    }
    
    function updateFieldProperty() {
        if (!selectedField) return;
        
        var sectionIndex = selectedField.sectionIndex;
        var fieldIndex = selectedField.fieldIndex;
        var field = formConfig.sections[sectionIndex].fields[fieldIndex];
        
        var $input = $(this);
        var id = $input.attr('id');
        var value = $input.val();
        
        switch (id) {
            case 'field_id':
                field.id = value;
                break;
            case 'field_label':
                field.label = value;
                break;
            case 'field_placeholder':
                field.placeholder = value;
                break;
            case 'field_rows':
                field.rows = parseInt(value) || 5;
                break;
            case 'field_required':
                field.required = $input.is(':checked');
                break;
            case 'field_css_class':
                field.css_class = value;
                break;
            case 'field_help_text':
                field.help_text = value;
                break;
            case 'field_file_types':
                if (!field.validation) field.validation = {};
                field.validation.file_types = value.split(',').map(function(type) {
                    return type.trim();
                }).filter(function(type) {
                    return type.length > 0;
                });
                break;
        }
        
        renderFormSections();
        selectField(null, sectionIndex, fieldIndex);
    }
    
    function updateSectionOrder() {
        var newOrder = [];
        $('#pco-form-sections .pco-form-section').each(function(index) {
            var sectionIndex = $(this).data('section-index');
            newOrder.push(formConfig.sections[sectionIndex]);
            $(this).data('section-index', index);
        });
        formConfig.sections = newOrder;
    }
    
    function updateFieldOrder() {
        // Update field order within sections
        $('.pco-form-section').each(function() {
            var sectionIndex = $(this).data('section-index');
            var newFields = [];
            
            $(this).find('.pco-form-field-item').each(function(index) {
                var fieldIndex = $(this).data('field-index');
                var originalSectionIndex = $(this).data('section-index');
                
                // Handle fields moved between sections
                if (originalSectionIndex !== sectionIndex) {
                    var field = formConfig.sections[originalSectionIndex].fields[fieldIndex];
                    newFields.push(field);
                    
                    // Remove from original section
                    formConfig.sections[originalSectionIndex].fields.splice(fieldIndex, 1);
                } else {
                    newFields.push(formConfig.sections[sectionIndex].fields[fieldIndex]);
                }
                
                $(this).data('field-index', index);
                $(this).data('section-index', sectionIndex);
            });
            
            formConfig.sections[sectionIndex].fields = newFields;
        });
    }
    
    function deleteField(event) {
        event.stopPropagation();
        
        if (!confirm(pco_form_builder_data.strings.confirm_delete_field)) {
            return;
        }
        
        var $field = $(this).closest('.pco-form-field-item');
        var sectionIndex = $field.data('section-index');
        var fieldIndex = $field.data('field-index');
        
        formConfig.sections[sectionIndex].fields.splice(fieldIndex, 1);
        
        renderFormSections();
        
        // Clear properties panel
        $('#pco-field-properties').html('<p>Wybierz pole, aby edytować jego właściwości.</p>');
        selectedField = null;
    }
    
    function deleteSection(event) {
        event.stopPropagation();
        
        if (!confirm(pco_form_builder_data.strings.confirm_delete_section)) {
            return;
        }
        
        var $section = $(this).closest('.pco-form-section');
        var sectionIndex = $section.data('section-index');
        
        formConfig.sections.splice(sectionIndex, 1);
        
        renderFormSections();
        
        // Clear properties panel
        $('#pco-field-properties').html('<p>Wybierz pole, aby edytować jego właściwości.</p>');
        selectedSection = null;
        selectedField = null;
    }
    
    function editSection(event) {
        event.stopPropagation();
        
        var $section = $(this).closest('.pco-form-section');
        var sectionIndex = $section.data('section-index');
        
        selectSection(null, sectionIndex);
    }
    
    // Add option functionality
    $(document).on('click', '.pco-add-option', function() {
        if (!selectedField) return;
        
        var sectionIndex = selectedField.sectionIndex;
        var fieldIndex = selectedField.fieldIndex;
        var field = formConfig.sections[sectionIndex].fields[fieldIndex];
        
        if (!field.options) field.options = [];
        
        field.options.push({ label: '', value: '' });
        
        showFieldProperties(field, sectionIndex, fieldIndex);
    });
    
    // Remove option functionality
    $(document).on('click', '.pco-remove-option', function() {
        if (!selectedField) return;
        
        var $item = $(this).closest('.pco-option-item');
        var optionIndex = $item.index();
        
        var sectionIndex = selectedField.sectionIndex;
        var fieldIndex = selectedField.fieldIndex;
        var field = formConfig.sections[sectionIndex].fields[fieldIndex];
        
        field.options.splice(optionIndex, 1);
        
        showFieldProperties(field, sectionIndex, fieldIndex);
    });
    
    // Update option values
    $(document).on('change', '.pco-option-item input', function() {
        if (!selectedField) return;
        
        var $item = $(this).closest('.pco-option-item');
        var optionIndex = $item.index();
        var property = $(this).data('property');
        var value = $(this).val();
        
        var sectionIndex = selectedField.sectionIndex;
        var fieldIndex = selectedField.fieldIndex;
        var field = formConfig.sections[sectionIndex].fields[fieldIndex];
        
        if (!field.options[optionIndex]) {
            field.options[optionIndex] = {};
        }
        
        field.options[optionIndex][property] = value;
    });
    
    function saveForm() {
        var $button = $('#pco-save-form');
        var originalText = $button.text();
        
        $button.text(pco_form_builder_data.strings.saving).prop('disabled', true);
        
        $.ajax({
            url: pco_form_builder_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pco_save_form_config',
                nonce: pco_form_builder_data.nonce,
                form_config: JSON.stringify(formConfig)
            },
            success: function(response) {
                if (response.success) {
                    $button.text(pco_form_builder_data.strings.saved);
                    setTimeout(function() {
                        $button.text(originalText).prop('disabled', false);
                    }, 2000);
                } else {
                    alert(response.data.message || pco_form_builder_data.strings.error);
                    $button.text(originalText).prop('disabled', false);
                }
            },
            error: function() {
                alert(pco_form_builder_data.strings.error);
                $button.text(originalText).prop('disabled', false);
            }
        });
    }
    
    function previewForm() {
        $.ajax({
            url: pco_form_builder_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pco_preview_form',
                nonce: pco_form_builder_data.nonce,
                form_config: JSON.stringify(formConfig)
            },
            success: function(response) {
                if (response.success) {
                    $('#pco-form-preview-content').html(response.data.html);
                    $('#pco-form-preview-modal').show();
                } else {
                    alert(response.data.message || pco_form_builder_data.strings.error);
                }
            },
            error: function() {
                alert(pco_form_builder_data.strings.error);
            }
        });
    }
    
    function escapeHtml(text) {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
});
