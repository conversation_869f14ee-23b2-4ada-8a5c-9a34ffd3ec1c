<?php
/**
 * Szablon strony formularza zamówienia
 */

// Zabezpieczenie przed bezpośrednim dostępem
if (!defined('ABSPATH')) {
    exit;
}

// Włączenie debugowania dla tego pliku
define('PCO_DEBUG', true);

// Sprawdzenie czy sesja WooCommerce jest aktywna
if (!WC()->session->has_session()) {
    pco_log('Brak aktywnej sesji WooCommerce na stronie formularza - próba inicjalizacji');
    WC()->session->set_customer_session_cookie(true);
}

// Sprawdzenie czy dane zamówienia są dostępne
$product_id = WC()->session->get('pco_product_id');
pco_log('Strona formularza - product_id z sesji: ' . ($product_id ? $product_id : 'brak'));

if (!$product_id) {
    pco_log('Brak danych produktu w sesji - przekierowanie na stronę główną');
    wp_redirect(home_url());
    exit;
}

get_header();
?>

<div class="pco-order-form-container">
    <div class="pco-order-form-wrapper">
        <h1><?php _e('Formularz zamówienia', 'papierotka-custom-order'); ?></h1>
        
        <?php
        // Wyświetlenie podsumowania zamówienia
        do_action('pco_before_order_form');
        ?>
        
        <form id="pco-order-form" class="pco-order-form">
            <?php
            // Dodanie ukrytych pól z danymi produktu
            $product_id = WC()->session->get('pco_product_id');
            $quantity = WC()->session->get('pco_quantity');
            $options_data = WC()->session->get('pco_options_data');
            $total_price = WC()->session->get('pco_total_price');
            ?>
            <input type="hidden" name="pco_product_id" value="<?php echo esc_attr($product_id); ?>">
            <input type="hidden" name="pco_quantity" value="<?php echo esc_attr($quantity); ?>">
            <input type="hidden" name="pco_options_data" value='<?php echo esc_attr(json_encode($options_data)); ?>'>
            <input type="hidden" name="pco_total_price" value="<?php echo esc_attr($total_price); ?>">
            
            <div class="pco-form-section">
                <h2><?php _e('Dane kontaktowe', 'papierotka-custom-order'); ?></h2>
                
                <div class="pco-form-field">
                    <label for="pco-name"><?php _e('Imię i nazwisko *', 'papierotka-custom-order'); ?></label>
                    <input type="text" id="pco-name" name="name" required>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-email"><?php _e('Adres e-mail *', 'papierotka-custom-order'); ?></label>
                    <input type="email" id="pco-email" name="email" required>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-phone"><?php _e('Telefon *', 'papierotka-custom-order'); ?></label>
                    <input type="tel" id="pco-phone" name="phone" required>
                </div>
            </div>
            
            <div class="pco-form-section">
                <h2><?php _e('Szczegóły zaproszenia', 'papierotka-custom-order'); ?></h2>
                
                <div class="pco-form-field">
                    <label for="pco-bride-name"><?php _e('Imię Panny Młodej *', 'papierotka-custom-order'); ?></label>
                    <input type="text" id="pco-bride-name" name="bride_name" required>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-groom-name"><?php _e('Imię Pana Młodego *', 'papierotka-custom-order'); ?></label>
                    <input type="text" id="pco-groom-name" name="groom_name" required>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-wedding-date"><?php _e('Data ślubu *', 'papierotka-custom-order'); ?></label>
                    <input type="date" id="pco-wedding-date" name="wedding_date" required>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-wedding-time"><?php _e('Godzina ślubu *', 'papierotka-custom-order'); ?></label>
                    <input type="time" id="pco-wedding-time" name="wedding_time" required>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-church-name"><?php _e('Nazwa kościoła / USC *', 'papierotka-custom-order'); ?></label>
                    <input type="text" id="pco-church-name" name="church_name" required>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-church-address"><?php _e('Adres kościoła / USC *', 'papierotka-custom-order'); ?></label>
                    <input type="text" id="pco-church-address" name="church_address" required>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-reception-name"><?php _e('Nazwa lokalu weselnego *', 'papierotka-custom-order'); ?></label>
                    <input type="text" id="pco-reception-name" name="reception_name" required>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-reception-address"><?php _e('Adres lokalu weselnego *', 'papierotka-custom-order'); ?></label>
                    <input type="text" id="pco-reception-address" name="reception_address" required>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-reception-time"><?php _e('Godzina rozpoczęcia wesela *', 'papierotka-custom-order'); ?></label>
                    <input type="time" id="pco-reception-time" name="reception_time" required>
                </div>
            </div>
            
            <div class="pco-form-section">
                <h2><?php _e('Lista gości', 'papierotka-custom-order'); ?></h2>
                
                <div class="pco-form-field">
                    <label for="pco-guest-list"><?php _e('Lista gości (opcjonalnie)', 'papierotka-custom-order'); ?></label>
                    <textarea id="pco-guest-list" name="guest_list" rows="10" placeholder="<?php _e('Wprowadź listę gości, każdy gość w nowej linii', 'papierotka-custom-order'); ?>"></textarea>
                </div>
                
                <div class="pco-form-field">
                    <label for="pco-guest-list-file"><?php _e('Lub załącz plik z listą gości', 'papierotka-custom-order'); ?></label>
                    <input type="file" id="pco-guest-list-file" name="guest_list_file">
                </div>
            </div>
            
            <div class="pco-form-section">
                <h2><?php _e('Dodatkowe informacje', 'papierotka-custom-order'); ?></h2>
                
                <div class="pco-form-field">
                    <label for="pco-additional-info"><?php _e('Dodatkowe informacje lub uwagi', 'papierotka-custom-order'); ?></label>
                    <textarea id="pco-additional-info" name="additional_info" rows="5"></textarea>
                </div>
            </div>
            
            <div class="pco-form-actions">
                <button type="submit" class="pco-submit-button"><?php _e('Złóż zamówienie', 'papierotka-custom-order'); ?></button>
            </div>
            
            <?php wp_nonce_field('pco-form-submission', 'pco_nonce'); ?>
        </form>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Obsługa przesyłania formularza
    $('#pco-order-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitButton = $form.find('.pco-submit-button');
        
        // Dezaktywacja przycisku
        $submitButton.prop('disabled', true).text('<?php _e('Przetwarzanie...', 'papierotka-custom-order'); ?>');
        
        // Pobranie danych formularza
        var formData = new FormData();
        var formDataObject = {};
        
        // Serializacja danych formularza do obiektu
        $form.serializeArray().forEach(function(item) {
            formDataObject[item.name] = item.value;
        });
        
        // Dodanie pliku z listą gości, jeśli został wybrany
        var guestListFile = $('#pco-guest-list-file')[0].files[0];
        if (guestListFile) {
            formData.append('guest_list_file', guestListFile);
        }
        
        // Dodanie pozostałych danych
        formData.append('action', 'pco_submit_form');
        formData.append('nonce', pco_data.nonce);
        formData.append('form_data', JSON.stringify(formDataObject));
        
        // Dodanie debugowania
        if (window.console && window.console.log) {
            console.log('Dane formularza:', formDataObject);
            console.log('ID produktu:', formDataObject.pco_product_id);
            console.log('Ilość:', formDataObject.pco_quantity);
            console.log('Opcje:', formDataObject.pco_options_data);
            console.log('Cena:', formDataObject.pco_total_price);
        }
        
        // Wysłanie danych
        $.ajax({
            url: pco_data.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Przekierowanie do strony potwierdzenia
                    window.location.href = response.data.redirect;
                } else {
                    // Wyświetlenie błędu
                    alert(response.data.message || '<?php _e('Wystąpił błąd podczas przetwarzania zamówienia. Spróbuj ponownie.', 'papierotka-custom-order'); ?>');
                    $submitButton.prop('disabled', false).text('<?php _e('Złóż zamówienie', 'papierotka-custom-order'); ?>');
                }
            },
            error: function() {
                // Wyświetlenie błędu
                alert('<?php _e('Wystąpił błąd podczas przetwarzania zamówienia. Spróbuj ponownie.', 'papierotka-custom-order'); ?>');
                $submitButton.prop('disabled', false).text('<?php _e('Złóż zamówienie', 'papierotka-custom-order'); ?>');
            }
        });
    });
});
</script>

<?php
get_footer();