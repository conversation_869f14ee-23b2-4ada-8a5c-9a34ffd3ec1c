/* Form Builder Admin Styles */

.pco-form-builder-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    min-height: 600px;
}

/* Toolbar */
.pco-form-builder-toolbar {
    width: 250px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.pco-form-builder-toolbar h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
}

.pco-field-types {
    margin-bottom: 20px;
}

.pco-field-type {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-bottom: 5px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: grab;
    transition: all 0.2s ease;
}

.pco-field-type:hover {
    background: #e7f3ff;
    border-color: #0073aa;
}

.pco-field-type.ui-draggable-dragging {
    cursor: grabbing;
    transform: rotate(5deg);
    z-index: 1000;
}

.pco-field-type .dashicons {
    margin-right: 8px;
    color: #666;
}

.pco-field-type .label {
    font-size: 13px;
    font-weight: 500;
}

.pco-form-actions {
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.pco-form-actions .button {
    width: 100%;
    margin-bottom: 8px;
    text-align: center;
}

/* Canvas */
.pco-form-builder-canvas {
    flex: 1;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    min-height: 600px;
}

.pco-form-section {
    border: 2px dashed #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    position: relative;
    transition: border-color 0.2s ease;
}

.pco-form-section:hover {
    border-color: #0073aa;
}

.pco-form-section.ui-droppable-hover {
    border-color: #00a32a;
    background-color: #f0f8f0;
}

.pco-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.pco-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
    margin: 0;
}

.pco-section-actions {
    display: flex;
    gap: 5px;
}

.pco-section-actions .button {
    padding: 2px 8px;
    font-size: 11px;
    height: auto;
    line-height: 1.4;
}

.pco-form-fields {
    min-height: 50px;
}

.pco-form-field-item {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 10px;
    margin-bottom: 10px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pco-form-field-item:hover {
    background: #e7f3ff;
    border-color: #0073aa;
}

.pco-form-field-item.selected {
    background: #e7f3ff;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.pco-form-field-item.ui-sortable-helper {
    transform: rotate(2deg);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.pco-field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.pco-field-label {
    font-weight: 500;
    color: #23282d;
}

.pco-field-type-badge {
    background: #666;
    color: #fff;
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 10px;
    text-transform: uppercase;
}

.pco-field-actions {
    display: flex;
    gap: 3px;
}

.pco-field-actions .button {
    padding: 1px 6px;
    font-size: 10px;
    height: auto;
    line-height: 1.2;
}

.pco-field-preview {
    font-size: 12px;
    color: #666;
}

/* Properties Panel */
.pco-form-builder-properties {
    width: 300px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.pco-form-builder-properties h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
}

.pco-property-group {
    margin-bottom: 20px;
}

.pco-property-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #23282d;
}

.pco-property-group input,
.pco-property-group textarea,
.pco-property-group select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.pco-property-group input:focus,
.pco-property-group textarea:focus,
.pco-property-group select:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.pco-property-group .description {
    font-size: 11px;
    color: #666;
    margin-top: 3px;
}

.pco-checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pco-checkbox-wrapper input[type="checkbox"] {
    width: auto;
}

/* Options List */
.pco-options-list {
    border: 1px solid #ddd;
    border-radius: 3px;
    max-height: 200px;
    overflow-y: auto;
}

.pco-option-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.pco-option-item:last-child {
    border-bottom: none;
}

.pco-option-item input {
    flex: 1;
    margin-right: 8px;
    border: none;
    background: transparent;
    font-size: 12px;
}

.pco-option-item .button {
    padding: 2px 6px;
    font-size: 10px;
    height: auto;
    line-height: 1.2;
}

.pco-add-option {
    margin-top: 8px;
    width: 100%;
}

/* Empty States */
.pco-empty-section {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.pco-empty-canvas {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    font-style: italic;
}

/* Drag and Drop States */
.pco-drag-over {
    border-color: #00a32a !important;
    background-color: #f0f8f0 !important;
}

.pco-drag-placeholder {
    height: 40px;
    background: #e7f3ff;
    border: 2px dashed #0073aa;
    border-radius: 3px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0073aa;
    font-size: 12px;
}

/* Modal Styles */
#pco-form-preview-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.pco-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    border-radius: 4px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.pco-modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    right: 15px;
    top: 10px;
    cursor: pointer;
}

.pco-modal-close:hover,
.pco-modal-close:focus {
    color: #000;
    text-decoration: none;
}

/* Form Preview */
.pco-form-preview {
    padding: 20px;
    background: #f9f9f9;
    border-radius: 4px;
}

.pco-form-preview .pco-order-form {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Loading States */
.pco-loading {
    opacity: 0.6;
    pointer-events: none;
}

.pco-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: pco-spin 1s linear infinite;
}

@keyframes pco-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .pco-form-builder-container {
        flex-direction: column;
    }
    
    .pco-form-builder-toolbar,
    .pco-form-builder-properties {
        width: 100%;
    }
    
    .pco-form-builder-toolbar {
        order: 1;
    }
    
    .pco-form-builder-canvas {
        order: 2;
    }
    
    .pco-form-builder-properties {
        order: 3;
    }
}

@media (max-width: 768px) {
    .pco-modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }
    
    .pco-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .pco-field-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
